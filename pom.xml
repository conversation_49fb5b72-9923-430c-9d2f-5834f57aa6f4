<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.digoak.ssss</groupId>
    <artifactId>oak-ssss</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <description>micronaut服务</description>

    <parent>
        <groupId>io.micronaut.platform</groupId>
        <artifactId>micronaut-parent</artifactId>
        <version>4.9.3</version>
    </parent>

    <properties>
        <jdk.version>17</jdk.version>
        <release.version>17</release.version>
        <micronaut.version>4.9.3</micronaut.version>
        <mapstruct.version>1.6.3</mapstruct.version>
        <mysql-connector.version>9.4.0</mysql-connector.version>
        <shedlock.version>5.16.0</shedlock.version>
        <platform.version>3.0.0-SNAPSHOT</platform.version>
        <micronaut.runtime>netty</micronaut.runtime>
        <micronaut.test.resources.enabled>true</micronaut.test.resources.enabled>
        <micronaut.aot.enabled>false</micronaut.aot.enabled>
        <micronaut.aot.packageName>micronaut.demo.aot.generated</micronaut.aot.packageName>
        <!-- 禁用 GraalVM Native 构建工具 相关功能 -->
        <skipNativeBuild>true</skipNativeBuild>
        <micronaut.processing.incremental>false</micronaut.processing.incremental>
    </properties>

    <dependencyManagement>
        <dependencies>

            <!--公共基础包-->
            <dependency>
                <groupId>com.digoak.boot</groupId>
                <artifactId>platform</artifactId>
                <version>${platform.version}</version>
            </dependency>

            <!--数据库驱动-->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql-connector.version}</version>
                <scope>runtime</scope>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>io.micronaut.testresources</groupId>
                <artifactId>micronaut-test-resources-client</artifactId>
                <version>2.10.1</version>
                <scope>provided</scope>
            </dependency>

            <!-- 兼容javax包 -->
            <dependency>
                <groupId>javax.transaction</groupId>
                <artifactId>javax.transaction-api</artifactId>
                <version>1.3</version>
            </dependency>

            <!-- 分布式定时任务 -->
            <dependency>
                <groupId>net.javacrumbs.shedlock</groupId>
                <artifactId>shedlock-micronaut</artifactId>
                <version>${shedlock.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>net.javacrumbs.shedlock</groupId>
                <artifactId>shedlock-provider-jdbc-micronaut</artifactId>
                <version>${shedlock.version}</version>
            </dependency>

            <!-- 转html -->
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>1.21.2</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!--公共基础包-->
        <dependency>
            <groupId>com.digoak.boot</groupId>
            <artifactId>platform</artifactId>
            <version>${platform.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>net.bytebuddy</groupId>
                    <artifactId>byte-buddy</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--client-->
        <dependency>
            <groupId>io.micronaut</groupId>
            <artifactId>micronaut-http-client</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.micronaut</groupId>
            <artifactId>micronaut-http-server-netty</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.micronaut</groupId>
            <artifactId>micronaut-jackson-databind</artifactId>
            <scope>compile</scope>
        </dependency>
        <!-- 数据库 -->
        <dependency>
            <groupId>io.micronaut.liquibase</groupId>
            <artifactId>micronaut-liquibase</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.micronaut.sql</groupId>
            <artifactId>micronaut-jdbc-hikari</artifactId>
            <scope>compile</scope>
        </dependency>
        <!-- Validation -->
        <dependency>
            <groupId>io.micronaut.validation</groupId>
            <artifactId>micronaut-validation</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jul-to-slf4j</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- 兼容javax包 -->
        <dependency>
            <groupId>javax.persistence</groupId>
            <artifactId>javax.persistence-api</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.transaction</groupId>
            <artifactId>javax.transaction-api</artifactId>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>HzWangdaRepo</id>
            <url>https://mvnrepository.hzwangda.com/repository/maven-public/</url>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>HzWangdaRepo</id>
            <url>https://mvnrepository.hzwangda.com/repository/maven-public/</url>
        </pluginRepository>
    </pluginRepositories>
    <distributionManagement>
        <repository>
            <id>HzWangdaRepo</id>
            <url>https://mvnrepository.hzwangda.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>HzWangdaRepo</id>
            <url>https://mvnrepository.hzwangda.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
