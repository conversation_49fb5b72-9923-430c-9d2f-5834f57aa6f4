package com.digoak.vehicle.model.controller;

import com.digoak.vehicle.model.domain.Manufacturer;
import com.digoak.vehicle.model.dto.ManufacturerDTO;
import com.digoak.vehicle.model.dto.ManufacturerQueryCriteria;
import com.digoak.vehicle.model.service.ManufacturerService;
import io.micronaut.core.annotation.Nullable;
import io.micronaut.http.HttpResponse;
import io.micronaut.http.annotation.*;
import io.micronaut.scheduling.TaskExecutors;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

/**
 * 制造商控制器
 */
@Slf4j
@Controller("/api/vehicle/manufacturers")
@ExecuteOn(TaskExecutors.BLOCKING)
@Tag(name = "制造商管理", description = "制造商相关API")
public class ManufacturerController {

    @Inject
    private ManufacturerService manufacturerService;

    @Post
    @Operation(summary = "创建制造商", description = "创建新的制造商")
    @ApiResponse(responseCode = "200", description = "创建成功")
    @ApiResponse(responseCode = "400", description = "请求参数错误")
    public HttpResponse<Manufacturer> create(@Valid @Body ManufacturerDTO manufacturerDTO) {
        log.info("保存制造商: {}", manufacturerDTO.getName());
        Manufacturer created = manufacturerService.save(manufacturerDTO);
        return HttpResponse.ok(created);
    }

    @Get("/{id}")
    @Operation(summary = "根据ID查询制造商", description = "根据ID查询制造商详情")
    @ApiResponse(responseCode = "200", description = "查询成功")
    @ApiResponse(responseCode = "404", description = "制造商不存在")
    public HttpResponse<Manufacturer> findById(@Parameter(description = "制造商ID") @PathVariable Long id) {
        log.info("查询制造商: {}", id);
        Optional<Manufacturer> manufacturer = manufacturerService.findById(id);
        return manufacturer.map(HttpResponse::ok)
                .orElse(HttpResponse.notFound());
    }

    @Get("/queryList")
    @Operation(summary = "查询所有制造商", description = "查询所有制造商列表，支持按名称和统一社会信用代码筛选")
    @ApiResponse(responseCode = "200", description = "查询成功")
    public HttpResponse<List<Manufacturer>> findAll(@Nullable @RequestBean ManufacturerQueryCriteria queryCriteria) {
        log.info("查询所有制造商，查询条件: {}", queryCriteria);
        List<Manufacturer> manufacturers = manufacturerService.findAll(queryCriteria);
        return HttpResponse.ok(manufacturers);
    }

    @Delete("/{id}")
    @Operation(summary = "删除制造商", description = "根据ID删除制造商")
    @ApiResponse(responseCode = "200", description = "删除成功")
    @ApiResponse(responseCode = "404", description = "制造商不存在")
    public HttpResponse<Void> deleteById(@Parameter(description = "制造商ID") @PathVariable Long id) {
        log.info("删除制造商: {}", id);
        if (!manufacturerService.existsById(id)) {
            return HttpResponse.notFound();
        }
        manufacturerService.deleteById(id);
        return HttpResponse.ok();
    }

    @Delete
    @Operation(summary = "批量删除制造商", description = "根据ID列表批量删除制造商")
    @ApiResponse(responseCode = "200", description = "删除成功")
    public HttpResponse<Void> deleteByIds(@Parameter(description = "制造商ID列表") @Body List<Long> ids) {
        log.info("批量删除制造商: {}", ids);
        manufacturerService.deleteByIds(ids);
        return HttpResponse.ok();
    }
}