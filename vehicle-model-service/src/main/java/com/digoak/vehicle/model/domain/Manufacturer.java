package com.digoak.vehicle.model.domain;

import com.digoak.boot.platform.base.BaseDomain;
import io.micronaut.data.annotation.MappedEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2025/9/8
 * @Description 车辆制造商
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@MappedEntity(value = "m_manufacturer")
@Schema(description = "车辆制造商实体")
public class Manufacturer extends BaseDomain {

    @Id
    @Column(name = "_id", unique = true)
    protected Long id;

    @NotBlank
    @Schema(description = "制造商名称", example = "宝马汽车")
    @Column(nullable = false, length = 64)
    private String name;

    @Schema(description = "统一社会信用代码", example = "91370200163567343M")
    @Column(length = 32)
    private String unifiedCreditCode;

    @Schema(description = "厂商Logo URL", example = "https://example.com/bmw-logo.jpg")
    @Column(length = 256)
    private String logo;

    @Schema(description = "制造商介绍", example = "德国豪华汽车制造商")
    @Column(length = 512)
    private String remark;

    @Schema(description = "厂商地址", example = "德国慕尼黑")
    @Column(length = 256)
    private String address;

    @Schema(description = "官网")
    @Column(length = 128)
    private String officialWebsite;
}
