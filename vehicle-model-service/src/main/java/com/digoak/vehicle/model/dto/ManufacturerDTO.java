package com.digoak.vehicle.model.dto;

import io.micronaut.core.annotation.Introspected;
import io.micronaut.serde.annotation.Serdeable;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Data;

/**
 * 制造商DTO
 */
@Data
@Builder
@Introspected
@Serdeable
@Schema(description = "制造商DTO")
public class ManufacturerDTO {

    @Schema(description = "制造商ID")
    private Long id;

    @NotBlank(message = "统一社会信用代码不能为空")
    @Size(max = 32, message = "统一社会信用代码长度不能超过32个字符")
    @Schema(description = "统一社会信用代码", required = true, example = "91370200163567343M")
    private String unifiedCreditCode;

    @NotBlank(message = "制造商名称不能为空")
    @Size(max = 64, message = "制造商名称长度不能超过64个字符")
    @Schema(description = "制造商名称", required = true, example = "宝马汽车")
    private String name;

    @Size(max = 256, message = "厂商Logo URL长度不能超过256个字符")
    @Schema(description = "厂商Logo URL", example = "https://example.com/bmw-logo.jpg")
    private String logo;

    @Size(max = 512, message = "备注长度不能超过512个字符")
    @Schema(description = "备注信息", example = "德国豪华汽车制造商")
    private String remark;

    @Schema(description = "厂商地址", example = "德国慕尼黑")
    @Size(max = 256, message = "厂商地址长度不能超过256个字符")
    private String address;

    @Schema(description = "官网")
    @Size(max = 128, message = "官网长度不能超过256个字符")
    private String officialWebsite;
}