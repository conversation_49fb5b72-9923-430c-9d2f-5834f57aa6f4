package com.digoak.vehicle.model.dto;

import com.digoak.vehicle.util.Query;
import io.micronaut.core.annotation.Introspected;
import io.micronaut.core.annotation.Nullable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * 制造商查询条件
 */
@Data
@ToString
@Introspected
@Schema(description = "制造商查询条件")
public class ManufacturerQueryCriteria {

    @Schema(description = "统一社会信用代码", example = "91370200163567343M")
    @Nullable
    @Query(type = Query.Type.EQUAL)
    private String unifiedCreditCode;

    @Schema(description = "制造商名称", example = "宝马汽车")
    @Nullable
    @Query(type = Query.Type.INNER_LIKE)
    private String name;

}