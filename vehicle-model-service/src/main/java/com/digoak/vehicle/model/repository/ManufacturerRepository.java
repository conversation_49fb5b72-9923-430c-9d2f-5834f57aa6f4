package com.digoak.vehicle.model.repository;

import com.digoak.vehicle.model.domain.Manufacturer;
import io.micronaut.data.mongodb.annotation.MongoRepository;
import io.micronaut.data.repository.CrudRepository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2025/9/8
 * @Description 制造商数据访问层
 **/
@MongoRepository
public interface ManufacturerRepository extends CrudRepository<Manufacturer, Long> {

    /**
     * 根据制造商名称查找制造商
     * @param name 制造商名称
     * @return 制造商信息
     */
    Optional<Manufacturer> findByName(String name);

    /**
     * 根据制造商名称模糊查询
     * @param name 制造商名称
     * @return 制造商列表
     */
    List<Manufacturer> findByNameContainsOrderByName(String name);

    /**
     * 根据统一社会信用代码查找制造商
     * @param unifiedCreditCode 统一社会信用代码
     * @return 制造商信息
     */
    Optional<Manufacturer> findByUnifiedCreditCode(String unifiedCreditCode);

    /**
     * 根据名称和统一社会信用代码进行复合查询
     * @param name 制造商名称（支持模糊查询）
     * @param unifiedCreditCode 统一社会信用代码（精确匹配）
     * @return 制造商列表
     */
    List<Manufacturer> findByNameContainsAndUnifiedCreditCode(String name, String unifiedCreditCode);

    /**
     * 根据名称进行模糊查询（忽略大小写）
     * @param name 制造商名称
     * @return 制造商列表
     */
    List<Manufacturer> findByNameContainsIgnoreCaseOrderByName(String name);
}