package com.digoak.vehicle.model.service.impl;

import com.digoak.boot.exception.CustomException;
import com.digoak.vehicle.model.domain.Manufacturer;
import com.digoak.vehicle.model.dto.ManufacturerDTO;
import com.digoak.vehicle.model.dto.ManufacturerQueryCriteria;
import com.digoak.vehicle.model.repository.ManufacturerRepository;
import com.digoak.vehicle.model.service.ManufacturerService;
import com.digoak.vehicle.util.QueryHelp;
import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 制造商服务实现类
 */
@Slf4j
@Singleton
@RequiredArgsConstructor
public class ManufacturerServiceImpl implements ManufacturerService {

    private final ManufacturerRepository manufacturerRepository;

    @Override
    public Manufacturer save(ManufacturerDTO manufacturerDTO) {
        log.info("保存制造商: {}", manufacturerDTO.getName());
        Manufacturer manufacturer;
        if (Objects.nonNull(manufacturerDTO.getId())) {
            manufacturer = manufacturerRepository.findById(manufacturerDTO.getId()).orElseThrow(() -> new CustomException("制造商不存在"));
        } else {
            manufacturer = new Manufacturer();
        }
        manufacturer.setUnifiedCreditCode(manufacturerDTO.getUnifiedCreditCode());
        manufacturer.setName(manufacturerDTO.getName());
        manufacturer.setAddress(manufacturerDTO.getAddress());
        manufacturer.setLogo(manufacturerDTO.getLogo());
        manufacturer.setRemark(manufacturerDTO.getRemark());
        manufacturer.setOfficialWebsite(manufacturerDTO.getOfficialWebsite());
        return manufacturerRepository.save(manufacturer);
    }

    @Override
    public Optional<Manufacturer> findById(Long id) {
        log.debug("根据ID查询制造商: {}", id);
        return manufacturerRepository.findById(id);
    }

    @Override
    public List<Manufacturer> findAll(ManufacturerQueryCriteria queryCriteria) {
        log.debug("查询所有制造商");
        return manufacturerRepository.findAll(((root, criteriaQuery, cb) -> QueryHelp.getPredicate(root, queryCriteria, cb)));
    }

    @Override
    public void deleteById(Long id) {
        log.info("删除制造商: {}", id);
        manufacturerRepository.deleteById(id);
    }

    @Override
    public void deleteByIds(List<Long> ids) {
        log.info("批量删除制造商: {}", ids);
        ids.forEach(manufacturerRepository::deleteById);
    }

    @Override
    public boolean existsById(Long id) {
        log.debug("检查制造商是否存在: {}", id);
        return manufacturerRepository.existsById(id);
        return true;
    }

}