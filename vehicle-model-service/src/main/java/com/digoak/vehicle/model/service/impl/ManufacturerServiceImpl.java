package com.digoak.vehicle.model.service.impl;

import com.digoak.boot.exception.CustomException;
import com.digoak.vehicle.model.domain.Manufacturer;
import com.digoak.vehicle.model.dto.ManufacturerDTO;
import com.digoak.vehicle.model.dto.ManufacturerQueryCriteria;
import com.digoak.vehicle.model.repository.ManufacturerRepository;
import com.digoak.vehicle.model.service.ManufacturerService;

import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 制造商服务实现类
 */
@Slf4j
@Singleton
@RequiredArgsConstructor
public class ManufacturerServiceImpl implements ManufacturerService {

    private final ManufacturerRepository manufacturerRepository;

    @Override
    public Manufacturer save(ManufacturerDTO manufacturerDTO) {
        log.info("保存制造商: {}", manufacturerDTO.getName());
        Manufacturer manufacturer;
        if (Objects.nonNull(manufacturerDTO.getId())) {
            manufacturer = manufacturerRepository.findById(manufacturerDTO.getId()).orElseThrow(() -> new CustomException("制造商不存在"));
        } else {
            manufacturer = new Manufacturer();
        }
        manufacturer.setUnifiedCreditCode(manufacturerDTO.getUnifiedCreditCode());
        manufacturer.setName(manufacturerDTO.getName());
        manufacturer.setAddress(manufacturerDTO.getAddress());
        manufacturer.setLogo(manufacturerDTO.getLogo());
        manufacturer.setRemark(manufacturerDTO.getRemark());
        manufacturer.setOfficialWebsite(manufacturerDTO.getOfficialWebsite());
        return manufacturerRepository.save(manufacturer);
    }

    @Override
    public Optional<Manufacturer> findById(Long id) {
        log.debug("根据ID查询制造商: {}", id);
        return manufacturerRepository.findById(id);
    }

    @Override
    public List<Manufacturer> findAll(ManufacturerQueryCriteria queryCriteria) {
        log.debug("查询所有制造商，查询条件: {}", queryCriteria);

        // 如果没有查询条件，返回所有数据
        if (queryCriteria == null) {
            return (List<Manufacturer>) manufacturerRepository.findAll();
        }

        String name = queryCriteria.getName();
        String unifiedCreditCode = queryCriteria.getUnifiedCreditCode();

        // 根据不同的查询条件组合进行查询
        if (name != null && !name.trim().isEmpty() && unifiedCreditCode != null && !unifiedCreditCode.trim().isEmpty()) {
            // 同时按名称和统一社会信用代码查询
            return manufacturerRepository.findByNameContainsAndUnifiedCreditCode(name.trim(), unifiedCreditCode.trim());
        } else if (name != null && !name.trim().isEmpty()) {
            // 仅按名称模糊查询
            return manufacturerRepository.findByNameContainsIgnoreCaseOrderByName(name.trim());
        } else if (unifiedCreditCode != null && !unifiedCreditCode.trim().isEmpty()) {
            // 仅按统一社会信用代码精确查询
            return manufacturerRepository.findByUnifiedCreditCode(unifiedCreditCode.trim())
                    .map(List::of)
                    .orElse(List.of());
        } else {
            // 没有有效查询条件，返回所有数据
            return (List<Manufacturer>) manufacturerRepository.findAll();
        }
    }

    @Override
    public void deleteById(Long id) {
        log.info("删除制造商: {}", id);
        manufacturerRepository.deleteById(id);
    }

    @Override
    public void deleteByIds(List<Long> ids) {
        log.info("批量删除制造商: {}", ids);
        ids.forEach(manufacturerRepository::deleteById);
    }

    @Override
    public boolean existsById(Long id) {
        log.debug("检查制造商是否存在: {}", id);
        return manufacturerRepository.existsById(id);
    }

}