package com.digoak.vehicle.util;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * MongoDB 查询帮助工具类
 * 专门用于 MongoDB 的查询条件构建
 * 
 * <AUTHOR>
 * @date 2025-09-12
 */
@Slf4j
@SuppressWarnings({"unchecked", "all"})
public class MongoQueryHelp {

    /**
     * 构建 MongoDB 查询条件
     * 注意：这个方法主要用于验证查询条件，实际的 MongoDB 查询应该使用 Repository 的方法名查询
     * 
     * @param query 查询条件对象
     * @return 是否有有效的查询条件
     */
    public static <Q> boolean hasValidQueryConditions(Q query) {
        if (query == null) {
            return false;
        }
        
        try {
            List<Field> fields = getAllFields(query.getClass(), new ArrayList<>());
            for (Field field : fields) {
                boolean accessible = field.isAccessible();
                field.setAccessible(true);
                
                Query q = field.getAnnotation(Query.class);
                if (q != null) {
                    Object val = field.get(query);
                    if (ObjectUtil.isNotNull(val) && !"".equals(val)) {
                        field.setAccessible(accessible);
                        return true;
                    }
                }
                field.setAccessible(accessible);
            }
        } catch (Exception e) {
            log.error("检查查询条件时发生错误: {}", e.getMessage(), e);
        }
        
        return false;
    }

    /**
     * 获取字段的查询值
     * 
     * @param query 查询对象
     * @param fieldName 字段名
     * @return 字段值
     */
    public static <Q> Object getFieldValue(Q query, String fieldName) {
        if (query == null || fieldName == null) {
            return null;
        }
        
        try {
            List<Field> fields = getAllFields(query.getClass(), new ArrayList<>());
            for (Field field : fields) {
                if (fieldName.equals(field.getName())) {
                    boolean accessible = field.isAccessible();
                    field.setAccessible(true);
                    Object value = field.get(query);
                    field.setAccessible(accessible);
                    return value;
                }
            }
        } catch (Exception e) {
            log.error("获取字段值时发生错误: {}", e.getMessage(), e);
        }
        
        return null;
    }

    /**
     * 获取带有 @Query 注解的字段值
     * 
     * @param query 查询对象
     * @param fieldName 字段名
     * @return 字段值，如果字段没有 @Query 注解或值为空则返回 null
     */
    public static <Q> Object getQueryFieldValue(Q query, String fieldName) {
        if (query == null || fieldName == null) {
            return null;
        }
        
        try {
            List<Field> fields = getAllFields(query.getClass(), new ArrayList<>());
            for (Field field : fields) {
                if (fieldName.equals(field.getName())) {
                    Query q = field.getAnnotation(Query.class);
                    if (q != null) {
                        boolean accessible = field.isAccessible();
                        field.setAccessible(true);
                        Object value = field.get(query);
                        field.setAccessible(accessible);
                        
                        // 返回非空且非空字符串的值
                        if (ObjectUtil.isNotNull(value) && !"".equals(value)) {
                            return value;
                        }
                    }
                    break;
                }
            }
        } catch (Exception e) {
            log.error("获取查询字段值时发生错误: {}", e.getMessage(), e);
        }
        
        return null;
    }

    /**
     * 检查字符串是否为空白
     */
    private static boolean isBlank(final CharSequence cs) {
        int strLen;
        if (cs == null || (strLen = cs.length()) == 0) {
            return true;
        }
        for (int i = 0; i < strLen; i++) {
            if (!Character.isWhitespace(cs.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取类的所有字段（包括父类）
     */
    public static List<Field> getAllFields(Class clazz, List<Field> fields) {
        if (clazz != null) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            getAllFields(clazz.getSuperclass(), fields);
        }
        return fields;
    }
}
