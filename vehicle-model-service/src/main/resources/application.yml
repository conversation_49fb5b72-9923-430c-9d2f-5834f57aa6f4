micronaut:
  application:
    name: vehicle-model
  server:
    cors:
      enabled: true
    context-path: /vehicle-model
    port: 8080
  http:
    client:
      connect-timeout: 3s
      read-timeout: 5s # 全局读取超时时间
  executors:
    scheduled:
      type: scheduled
      core-pool-size: 20
  test:
    resources:
      enabled: false
    testcontainers:
      enabled: false

jpa:
  default:
    properties:
      hibernate:
        hbm2ddl:
          auto: update
        show_sql: true

# MongoDB 配置
mongodb:
  uri: ********************************************************************
  connectionPool:
    maxSize: 20

liquibase:
  enabled: false
  datasources:
    default:
      change-log: classpath:db/liquibase-changelog.xml