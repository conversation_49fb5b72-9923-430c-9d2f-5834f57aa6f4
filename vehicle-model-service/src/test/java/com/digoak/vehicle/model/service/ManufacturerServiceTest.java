package com.digoak.vehicle.model.service;

import com.digoak.vehicle.model.dto.ManufacturerQueryCriteria;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 制造商服务测试类
 * 主要测试查询条件的构建逻辑
 */
@MicronautTest
class ManufacturerServiceTest {

    @Test
    void testQueryCriteriaCreation() {
        // 测试查询条件对象的创建
        ManufacturerQueryCriteria criteria = new ManufacturerQueryCriteria();
        assertNotNull(criteria);

        // 测试设置名称查询条件
        criteria.setName("一汽");
        assertEquals("一汽", criteria.getName());

        // 测试设置统一社会信用代码查询条件
        criteria.setUnifiedCreditCode("91370200163567343M");
        assertEquals("91370200163567343M", criteria.getUnifiedCreditCode());
    }

    @Test
    void testQueryCriteriaWithNullValues() {
        // 测试空值处理
        ManufacturerQueryCriteria criteria = new ManufacturerQueryCriteria();
        assertNull(criteria.getName());
        assertNull(criteria.getUnifiedCreditCode());

        // 测试设置空字符串
        criteria.setName("");
        criteria.setUnifiedCreditCode("");
        assertEquals("", criteria.getName());
        assertEquals("", criteria.getUnifiedCreditCode());
    }

    @Test
    void testQueryCriteriaWithWhitespace() {
        // 测试空白字符处理
        ManufacturerQueryCriteria criteria = new ManufacturerQueryCriteria();

        criteria.setName("  一汽  ");
        criteria.setUnifiedCreditCode("  91370200163567343M  ");

        assertEquals("  一汽  ", criteria.getName());
        assertEquals("  91370200163567343M  ", criteria.getUnifiedCreditCode());
    }

    @Test
    void testQueryCriteriaToString() {
        // 测试 toString 方法
        ManufacturerQueryCriteria criteria = new ManufacturerQueryCriteria();
        criteria.setName("一汽");
        criteria.setUnifiedCreditCode("91370200163567343M");

        String result = criteria.toString();
        assertNotNull(result);
        assertTrue(result.contains("一汽"));
        assertTrue(result.contains("91370200163567343M"));
    }
}
